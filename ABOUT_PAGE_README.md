# About Page with Ghost.io Sync

A modern, professional About page with photo placeholder, enhanced bio content, social media cards, and automatic Ghost.io synchronization.

## ✨ Features

- **Professional Hero Section** with photo placeholder and key information
- **Enhanced Bio Content** highlighting open source experience and expertise
- **Social Media Cards** with platform-specific styling and descriptions
- **Achievements Section** showcasing key milestones
- **Call-to-Action Section** for GitHub Sponsors and projects
- **Responsive Design** that works on all devices
- **Ghost.io Integration** for automatic page updates

## 🚀 Quick Start

### Update About Page

```bash
# Update the About page on Ghost.io
./scripts/update-about.sh

# Preview locally before updating
./scripts/update-about.sh --preview

# Dry run to see what would be updated
node scripts/ghost.js --page about --dry-run
```

### Manual Content Updates

Edit the content in `data/about.json`:

```json
{
  "personal": {
    "name": "Your Name",
    "title": "Your Title",
    "currentPosition": "Your Current Position",
    "photoUrl": "/path/to/your/photo.jpg"
  },
  "bio": {
    "intro": "Your introduction...",
    "expertise": ["Skill 1", "Skill 2", "Skill 3"]
  },
  "socialMedia": [
    {
      "platform": "GitHub",
      "url": "https://github.com/yourusername",
      "description": "Your GitHub description"
    }
  ]
}
```

## 📁 File Structure

```
├── data/
│   └── about.json              # About page content and configuration
├── templates/
│   ├── pages/
│   │   └── about.html          # Main About page template
│   ├── components/
│   │   └── social-card.html    # Social media card component
│   └── styles/
│       └── about.css           # About page styling
└── scripts/
    ├── ghost.js                # Main sync script (updated)
    └── update-about.sh         # Convenient update script
```

## 🎨 Design Features

### Hero Section
- Photo placeholder with fallback styling
- Name, title, and current position
- Key metadata (location, experience)
- Clean, professional layout

### Bio Sections
- Highlighted introduction
- Expertise list with visual styling
- Professional experience description
- Open source contributions highlight
- Speaking and writing experience

### Social Media Cards
- Platform-specific icons and colors
- Descriptive text for each platform
- Hover effects and smooth transitions
- Responsive grid layout

### Achievements
- Visual achievement cards
- Icons and descriptions
- Year indicators
- Hover animations

### Call-to-Action
- Gradient background
- Primary and secondary buttons
- GitHub Sponsors integration
- Link to open source projects

## 🔧 Customization

### Adding New Social Platforms

1. Add to `data/about.json`:
```json
{
  "platform": "Platform Name",
  "url": "https://platform.com/profile",
  "handle": "@username",
  "description": "What to find on this platform",
  "icon": "platform-icon-name",
  "color": "#brand-color"
}
```

2. Add icon SVG to `templates/components/social-card.html`:
```html
{{#if (eq icon "new-platform")}}
<svg viewBox="0 0 24 24" fill="currentColor">
  <!-- Platform icon SVG path -->
</svg>
{{/if}}
```

### Updating Content

Edit `data/about.json` and run:
```bash
./scripts/update-about.sh
```

### Styling Changes

Modify `templates/styles/about.css` for visual customizations.

## 🌐 Ghost.io Integration

### Automatic Sync

The About page integrates with Ghost.io using the same system as the GitHub Sponsors and Open Source pages:

```bash
# Update About page
node scripts/ghost.js --page about

# Dry run
node scripts/ghost.js --page about --dry-run

# Verbose output
node scripts/ghost.js --page about --verbose
```

### Manual Sync (Fallback)

If API sync fails:

1. Run the script (it generates `about-manual-update.html`)
2. Copy the HTML content
3. Go to Ghost Admin → Pages → About
4. Delete current content and add an HTML card
5. Paste the HTML content
6. Publish the page

### Setup Ghost.io API

1. Go to Ghost Admin → Settings → Integrations
2. Create a new Custom Integration
3. Copy the Admin API Key
4. Update `ghost.yml`:
```yaml
integrations:
  page_sync:
    admin_api_key: "your_admin_api_key_here"
```

## 📱 Responsive Design

The About page is fully responsive:

- **Desktop**: Two-column hero layout, multi-column grids
- **Tablet**: Adjusted spacing and grid layouts
- **Mobile**: Single-column layout, stacked elements

## 🎯 SEO & Performance

- Semantic HTML structure
- Optimized CSS with embedded styles
- Fast loading with minimal external dependencies
- Social media meta tags support
- Accessible design with proper ARIA labels

## 🔄 Maintenance

### Regular Updates

1. Update content in `data/about.json`
2. Run `./scripts/update-about.sh`
3. Verify the page at https://solnic.dev/about/

### Adding New Achievements

Add to the `achievements` array in `data/about.json`:
```json
{
  "title": "Achievement Title",
  "description": "Achievement description",
  "year": "2024",
  "icon": "🏆"
}
```

### Photo Updates

1. Add your photo to `/assets/images/`
2. Update `photoUrl` in `data/about.json`
3. Run the update script

## 🚀 Next Steps

- [ ] Add more social platforms as needed
- [ ] Customize achievements and bio content
- [ ] Add your actual photo
- [ ] Set up automatic updates via GitHub Actions
- [ ] Consider adding testimonials or recommendations section

## 💡 Tips

- Use high-quality photos (recommended: 400x400px minimum)
- Keep social media descriptions concise but informative
- Update achievements regularly to keep content fresh
- Test the page on different devices after updates
- Use the preview feature to check changes before publishing
