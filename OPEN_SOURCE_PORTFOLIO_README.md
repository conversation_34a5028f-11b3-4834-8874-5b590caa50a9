# Open Source Portfolio

A beautiful, interactive portfolio showcasing 15 years of open source contributions to Ruby, Elixir, and modern software architecture.

## Features

- **Interactive Project Cards**: Expandable cards with detailed project information
- **Real-time GitHub Statistics**: Total commits, repositories, and organization memberships
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Project Filtering**: Filter by current/past projects, frameworks, libraries, etc.
- **Professional Design**: Modern, clean interface with smooth animations

## Files Structure

```
├── static/open-source-portfolio.html    # Main portfolio HTML file
├── data/
│   ├── open-source-projects.json       # Structured project data
│   └── github-stats.json               # GitHub contribution statistics
├── content/open-source/images/          # Project logos
│   ├── dry-rb-round-150x150.png
│   └── rom_avatar_440x440-150x150.png
└── scripts/gh.js                       # Extended GitHub API script
```

## Getting Started

### 1. View the Portfolio

Open `static/open-source-portfolio.html` in your browser:

```bash
open static/open-source-portfolio.html
```

The portfolio will load with sample data and fallback statistics.

### 2. Fetch Real GitHub Statistics

To get real GitHub contribution statistics:

1. **Get a GitHub Personal Access Token**:
   - Go to https://github.com/settings/tokens
   - Create a new token with `user:read` scope
   - Copy the token

2. **Set up environment**:
   ```bash
   export GITHUB_API_KEY="your_github_token_here"
   ```

3. **Fetch statistics**:
   ```bash
   # Fetch stats for the authenticated user
   node scripts/gh.js --stats --verbose

   # Or fetch stats for a specific user
   node scripts/gh.js --stats --username solnic --verbose
   ```

4. **Refresh the portfolio** to see real statistics

### 3. Customize Project Data

Edit `data/open-source-projects.json` to:
- Add new projects
- Update project descriptions
- Add project logos
- Modify project categories and types

### 4. Add Project Logos

Place project logos in `content/open-source/images/` and reference them in the JSON:

```json
{
  "logo": "your-project-logo.png"
}
```

If no logo is specified, a GitHub icon will be used as fallback.

## GitHub Statistics Script

The extended `scripts/gh.js` now supports fetching contribution statistics:

### Available Commands

```bash
# Fetch contribution statistics
node scripts/gh.js --stats

# Dry run to see what would be fetched
node scripts/gh.js --stats --dry-run

# Verbose output with detailed information
node scripts/gh.js --stats --verbose

# Fetch stats for specific user
node scripts/gh.js --stats --username username

# Custom output file
node scripts/gh.js --stats --stats-output data/my-stats.json
```

### What Statistics Are Fetched

- **Total Contributions**: Sum of commits across multiple years (configurable)
- **Current Year Contributions**: Commits in the current year
- **Total Repositories**: Number of repositories contributed to
- **Public Repositories**: Count of public repositories
- **Organizations**: List and count of organization memberships
- **Yearly Breakdown**: Contribution history by year
- **Repository Sample**: List of notable repositories

### Rate Limiting

The script includes:
- Automatic delays between API calls
- Error handling for rate limits
- Graceful fallbacks for missing data

## Customization

### Adding New Project Categories

1. Update the filter buttons in the HTML
2. Add corresponding filter logic in JavaScript
3. Add CSS classes for new badge types

### Styling Customization

The portfolio uses CSS custom properties (variables) for easy theming:

```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --accent-color: #0ea5e9;
    /* ... more variables */
}
```

### Project Data Schema

Each project should follow this structure:

```json
{
  "id": "unique-project-id",
  "name": "Project Name",
  "category": "current|past",
  "type": "framework|library|organization",
  "description": "Project description",
  "technologies": ["Ruby", "Elixir"],
  "status": "active|discontinued",
  "role": "Creator|Maintainer|Core Team Member",
  "year_started": 2020,
  "year_ended": 2023,
  "links": {
    "github": "https://github.com/...",
    "website": "https://...",
    "blog_post": "https://..."
  },
  "logo": "project-logo.png",
  "highlights": [
    "Key feature 1",
    "Key feature 2"
  ],
  "sub_projects": [
    {
      "name": "Sub-project Name",
      "description": "Sub-project description",
      "github": "https://github.com/..."
    }
  ]
}
```

## Ghost.io Integration

The portfolio is now fully integrated with Ghost.io and can be synced automatically!

### Setup Ghost.io Sync

1. **Configure Ghost.io API**:
   - Go to your Ghost Admin → Settings → Integrations
   - Create a new Custom Integration
   - Copy the Admin API Key
   - Update `ghost.yml` with your API key:
   ```yaml
   integrations:
     page_sync:
       admin_api_key: "your_admin_api_key_here"
   ```

2. **Sync the Portfolio**:
   ```bash
   # Update the open source page on Ghost.io
   node scripts/ghost.js --page open-source

   # Dry run to see what would be updated
   node scripts/ghost.js --page open-source --dry-run

   # Verbose output for debugging
   node scripts/ghost.js --page open-source --verbose
   ```

3. **Automated Updates**:
   ```bash
   # Update GitHub stats and sync to Ghost.io
   ./scripts/update-portfolio.sh
   ```

### What Gets Synced

The Ghost.io integration creates a beautiful, responsive portfolio page with:

- **Live GitHub Statistics**: Real contribution data from the GitHub API
- **Project Cards**: Interactive cards with logos, descriptions, and links
- **Responsive Design**: Optimized for Ghost.io's theme system
- **Embedded CSS**: All styling included for perfect rendering
- **SEO Optimized**: Proper structure for search engines

### Ghost.io Page Structure

The synced page includes:
- Hero section with contribution statistics
- Current projects section with active projects
- Past projects section with discontinued but notable projects
- Responsive grid layout that works with any Ghost theme
- Embedded CSS that doesn't conflict with theme styles

### Manual Sync (Fallback)

If API sync fails, the script generates a fallback HTML file:

```bash
# This creates open-source-manual-update.html
node scripts/ghost.js --page open-source
```

Then manually:
1. Copy the HTML content from the generated file
2. Go to Ghost Admin → Pages → Open Source
3. Delete current content and add an HTML card
4. Paste the HTML content
5. Publish the page

## Deployment

### Ghost.io (Recommended)

The portfolio is designed to work perfectly with Ghost.io:

1. Use the automated sync: `node scripts/ghost.js --page open-source`
2. The page will be live at `https://yourdomain.com/open-source/`
3. Updates are instant and preserve your Ghost theme styling

### Static Hosting

The portfolio can also be hosted as a static file:

- GitHub Pages
- Netlify
- Vercel
- Any web server

### Hugo Integration

For Hugo sites (like this project):

1. The portfolio data is in `data/open-source-projects.json`
2. Use Hugo's data files and templates
3. Generate during Hugo build process

## Troubleshooting

### GitHub API Issues

- **Authentication Failed**: Check your `GITHUB_API_KEY` environment variable
- **Rate Limiting**: The script includes delays, but you may need to wait if you hit limits
- **Missing Data**: Some statistics may not be available for all users

### Loading Issues

- **Projects Not Loading**: Check that `data/open-source-projects.json` is accessible
- **Stats Not Loading**: Ensure `data/github-stats.json` exists or run the stats script
- **Images Not Loading**: Verify image paths in `content/open-source/images/`

## Contributing

To add new features or fix issues:

1. Update the HTML/CSS/JavaScript as needed
2. Test with sample data
3. Update the project data schema if necessary
4. Update this README with any new features

## License

This portfolio template is open source and can be adapted for your own use.
