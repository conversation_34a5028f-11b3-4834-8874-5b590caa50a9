---
title: Retiring from the core teams
date: '2024-12-12'
tags:
- ruby
- opensource
- announcement
- hanami
- dry-rb
- rom-rb
slug: retiring-from-the-core-teams
aliases:
- "/2024/12/12/retiring-from-the-core-teams"
- "/retiring-from-the-core-teams"
---

Today [we’re announcing on the official Hanami blog](https://hanamirb.org/blog/2024/12/12/a-new-chapter-for-dry-rb-rom/) that I am retiring from the core teams. It’s a decision I’ve made after about 2 years of contemplating how I want to move forward with my involvement with the Ruby community and its Open Source ecosystem.

As you can imagine, this decision has not been easy to make. I have a long history of working with Ruby and being an active member of its Open Source community. I will do my best to tell you why I made this decision, but first and foremost, this post is about the people who changed my life and a way of saying thank you. Let's start with that!

This is in somewhat chronological order:

* <PERSON> - thank you for believing in me back in 2007 when you hired me as a Ruby on Rails developer, even though the only thing I had written in <PERSON> was a sample app for my Bachelor thesis. I spent an amazing 3.5 years working at [Lunar Logic](https://lunarlogic.com) - the longest time I’ve ever worked for a single company, and I’ve grown a lot there.

* All the people from Lunar Logic (called Lunar Logic Polska back then) - folks, you have no idea what it meant to work with you. I’m grateful that I’m still in touch with some of you.

* <PERSON> <PERSON>bb - thank you for being my mentor and inviting me to the <PERSON><PERSON>apper core team. This is when everything really started for me, and it’s had a lasting effect on pretty much everything I’ve done later on.

* <PERSON> <PERSON>uidi - 11 years ago we had a call, and you told me about <PERSON> (now Hanami). Time flies! Like you said, it’s been a wild ride. Thank you so much for your hard work, trust, support, and patience. Ruby has been lucky to have you, and I wish you all the best in the next chapter of your life.

* Andy Holland - thank you for starting dry-rb! It grew so much over time, but it all started with you and dry-container + dry-configurable! It was so great to meet you in person during Brighton Ruby, and I hope you’re doing well.

* Nikita Shilnikov - thank you for joining me and working so hard on rom-rb and dry-rb. I suspect many people have no clue about the impact your work has had on these projects.

* Maciej Mensfeld - thank you for the time we spent together sharing offices, all the support and for being an amazing KRUG organizer! I wish you all the best with your [Karafka](https://karafka.io) project!


## Thank you Tim!

Special thanks go to Tim Riley - remember that email you sent to me 9 years ago about Transflow and a gem you were building inspired by it? This is where everything started for us. I'm so unbelievably proud of what we've accomplished together, and I truly wish I could continue working on Hanami with you.

I wish you, the entire team, and all the contributors all the best. I'll support you however I can, and I'm very happy to see how Hanami has evolved under your leadership.

## Community

I wasn’t sure if I wanted to thank specific people out of fear that I’d miss someone, but I decided to do it anyway. However, because of this, I also want to thank the entire community and *literally everybody* who worked with me, supported me, all the people who loved my work, and those who hated it (for real) - all of you have made an impact on my life, career, and even specific skills that I developed, and I am grateful for this.

Over the years, I’ve worked with hundreds of people through my work on OSS, and it’s an experience that shaped my personality and helped me grow. This is priceless.

I will genuinely miss y’all, but at the same time, I am sure that I’m doing what’s best for me.

## GitHub Sponsors

Special thanks go to all my past and present GitHub Sponsors, in alphabetical order:

* [about source GmbH](https://github.com/aboutsource)

* [Adam Piotrowski](https://github.com/panSarin)

* [Aldis Berjoza](https://github.com/graudeejs)

* [Avo](https://github.com/avo-hq)

* [Benjamin Klotz](https://github.com/tak1n)

* [blafri](https://github.com/blafri)

* [Bohdan V.](https://github.com/g3d)

* [Cabourn](https://github.com/cabourn)

* [Caius Durling](https://github.com/caius)

* [Chris Salzberg](https://github.com/shioyama)

* [Culture Amp GitHub Sponsorships](https://github.com/cultureamp-sponsorships)

* [David Pelaez](https://github.com/davidpelaez)

* [Eimantas](https://github.com/eimantas)

* [Ernest Bursa](https://github.com/swistaczek)

* [François Beausoleil](https://github.com/francois)

* [Gabriel Malaquias](https://github.com/GabrielMalakias)

* [Gar 🦖](https://github.com/edgarjs)

* [Hasan Kumar](https://github.com/mintuhouse)

* [Hiroshi SHIBATA](https://github.com/hsbt)

* [i2chris](https://github.com/i2chris)

* [Igor Pstyga](https://github.com/opti)

* [Igor S. Morozov](https://github.com/Morozzzko)

* [jan](https://github.com/yuszuv)

* [Janko Marohnić](https://github.com/janko)

* [Jason Charnes](https://github.com/jasoncharnes)

* [Kamil Skrzypiński](https://github.com/skarlcf)

* [Karafka](https://github.com/karafka)

* [Kasper Meyer](https://github.com/kaspermeyer)

* [Kirill Zaitsev](https://github.com/kzaitsev)

* [Konstantin Rudy](https://github.com/k-rudy)

* [Maarten Claes](https://github.com/mcls)

* [Maciej Mensfeld](https://github.com/mensfeld)

* [Marco Roth](https://github.com/marcoroth)

* [Michał Matyas](https://github.com/d4rky-pl)

* [Oleksandra Tomilina](https://github.com/aleksandra-stolyar)

* [Oleksii Leonov](https://github.com/oleksii-leonov)

* [Peter Berkenbosch](https://github.com/peterberkenbosch)

* [Prowly](https://github.com/prowlycom)

* [roma](https://github.com/milushov)

* [Rostislav Zhuravsky](https://github.com/woarewe)

* [Ruslan Tolstov](https://github.com/ruslantolstov)

* [Ryan Bigg](https://github.com/radar)

* [Sahil Gadimbayli](https://github.com/gadimbaylisahil)

* [Scout Monitoring Sponsorships](https://github.com/scoutapm-sponsorships)

* [Sean Collins](https://github.com/cllns)

* [Seb Wilgosz](https://github.com/swilgosz)

* [Sentry](https://github.com/getsentry)

* [Sven Schwyn](https://github.com/svoop)

* [Thomas Carr](https://github.com/htcarr3)

* [Thomas Klemm](https://github.com/thomasklemm)

* [Timo Sulg](https://github.com/timgluz)

* [Tom Donarski](https://github.com/tomdonarski)

* [Vasily Kolesnikov](https://github.com/v-kolesnikov)

* [Vladimir Kalinkin](https://github.com/cylon-v)

* [Wilson Silva](https://github.com/wilsonsilva)

* [Yuriy Kharchenko](https://github.com/letmein)

* [Ziyan Junaideen](https://github.com/ziyan-junaideen)


I will be sending an update to my current sponsors, to make sure people are aware of the change of my OSS focus.

## Moving on

Explaining why I’ve made this decision is going to be hard. I’m a bit nervous because I don’t want this to sound like I no longer believe in what I had built and contributed to, because I still believe that the FP/OO experiment I started back in 2013/2014 panned out well.

The reason for the decision is more related to how much time I have for Ruby and what my priorities are. One of my priorities is to make my life simpler and more balanced so that I can be a better partner and father. I've found that being active in the Ruby community can be too draining for me, just because of the number of projects I've been involved with, and other non-technical reasons.

I realized that despite my best intentions, I have not managed to contribute in any significant way since around 2021. Coincidentally, this is the year when I started working with Elixir full-time. This realization gave me a lot to think about, and finally, I decided to pull the plug earlier this year.

Working with Elixir has been very refreshing and has given me energy that I hadn't felt when working with Ruby for quite some time. Sure, working on Hanami gets pretty close to this, but it almost always came with this weird pressure. After some time, I just couldn't handle it well.

In Elixir, things are just simpler for me - I don’t need to work on ROM; there’s Ecto. I don’t need to work on Hanami; there’s Phoenix. I don’t need to have constant “battles” about the functional way of writing code and immutable data structures being The Way™, because, well 👉🏻 Elixir.

It’s harmony, and it’s beautiful.

This is not news to me either. I was fully aware of this for years, but when I started working with Elixir, that's when I really felt it. This experience made me believe even more strongly that **Hanami has the potential to grow into a truly groundbreaking Ruby framework**, and I think it’s headed this way. The foundational technology is in place; now it’s only a matter of growing the framework into a delightful DX, and it has already achieved that to a large degree, in my opinion.

I’m still rooting for Hanami, but I simply do not have the time and emotional capacity to be an active member of the core teams. I prioritize my well-being and my family over my work ambitions.

## What exactly is happening now

As you could read in the official announcement, I’m no longer a Hanami/dry-rb/rom-rb core team member. What this means in practice is that I won’t be doing any coding work or be active in other ways within the Hanami community. I will, however, support Hanami by providing information and guidance whenever it’s needed, as I’ll still be in touch with Tim and the team.

**This does not mean I’m removing myself completely from the Ruby community.** I’m currently working as an SDK maintainer at Sentry, specifically for Elixir **and** Ruby, which means there’s a chance we’ll bump into each other every now and then.

I hope this makes sense to you, and if you have any questions, do not hesitate to ask. You can find me on social media:

* [Bsky](https://bsky.app/profile/solnic.dev)

* [Mastodon](https://hachyderm.io/@solnic)

* [LinkedIn](https://www.linkedin.com/in/solnic/)

* [X](https://x.com/solnic_dev)


Thanks for reading, and I wish you a great day 🙂
