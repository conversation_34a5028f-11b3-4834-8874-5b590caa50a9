const fs = require('fs');
const path = require('path');

/**
 * Simple template engine using ES6 template literals
 * Supports variable interpolation, loops, and component includes
 */
class TemplateEngine {
  constructor(templatesDir = 'templates') {
    this.templatesDir = path.resolve(templatesDir);
    this.templateCache = new Map();
  }

  /**
   * Load a template file and cache it
   */
  loadTemplate(templatePath) {
    const fullPath = path.join(this.templatesDir, templatePath);

    if (this.templateCache.has(fullPath)) {
      return this.templateCache.get(fullPath);
    }

    if (!fs.existsSync(fullPath)) {
      throw new Error(`Template not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf8');
    this.templateCache.set(fullPath, content);
    return content;
  }

  /**
   * Process template syntax and convert to ES6 template literal
   */
  processTemplate(template, data = {}) {
    let processed = template;

    // Handle each loops: {{#each items}}...{{/each}} - PROCESS FIRST
    processed = processed.replace(/\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g, (match, arrayName, content) => {
      const array = this.getNestedProperty(data, arrayName.trim());
      if (!Array.isArray(array)) {
        return '';
      }

      return array.map(item => {
        // Create a new data context with the current item
        const itemData = { ...data, this: item, ...item };
        return this.processTemplate(content, itemData);
      }).join('');
    });

    // Handle component includes: {{>component-name}} - PROCESS AFTER LOOPS
    processed = processed.replace(/\{\{>\s*([^}]+)\s*\}\}/g, (match, componentName) => {
      const componentPath = `components/${componentName.trim()}.html`;
      const componentTemplate = this.loadTemplate(componentPath);
      return this.processTemplate(componentTemplate, data);
    });

    // Handle conditional blocks: {{#if condition}}...{{else}}...{{/if}}
    processed = processed.replace(/\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (match, condition, content) => {
      const value = this.getNestedProperty(data, condition.trim());

      // Check if there's an {{else}} block
      const elseMatch = content.match(/^([\s\S]*?)\{\{else\}\}([\s\S]*)$/);

      if (elseMatch) {
        // Has {{else}} block
        const [, ifContent, elseContent] = elseMatch;
        return value ? this.processTemplate(ifContent, data) : this.processTemplate(elseContent, data);
      } else {
        // No {{else}} block
        return value ? this.processTemplate(content, data) : '';
      }
    });

    // Handle variable interpolation: {{variable}} or {{object.property}}
    processed = processed.replace(/\{\{([^#/>][^}]*)\}\}/g, (match, variable) => {
      const value = this.getNestedProperty(data, variable.trim());
      return value !== undefined ? String(value) : '';
    });

    return processed;
  }

  /**
   * Get nested property from object using dot notation
   */
  getNestedProperty(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Render a template with data
   */
  render(templatePath, data = {}) {
    const template = this.loadTemplate(templatePath);
    return this.processTemplate(template, data);
  }

  /**
   * Render a page template with embedded CSS
   */
  renderPage(pageName, data = {}) {
    const pageTemplate = this.render(`pages/${pageName}.html`, data);

    // Check if there's a corresponding CSS file
    const cssPath = path.join(this.templatesDir, 'styles', `${pageName}.css`);
    let css = '';

    if (fs.existsSync(cssPath)) {
      css = fs.readFileSync(cssPath, 'utf8');
    }

    // Embed CSS in the page
    if (css) {
      const styleTag = `<style>\n${css}\n</style>\n\n`;
      return styleTag + pageTemplate;
    }

    return pageTemplate;
  }

  /**
   * Clear template cache (useful for development)
   */
  clearCache() {
    this.templateCache.clear();
  }
}

module.exports = TemplateEngine;
