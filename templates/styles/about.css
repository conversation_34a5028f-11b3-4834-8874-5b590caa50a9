/* FontAwesome for social icons */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/brands.min.css');

/* About Page Styles */
.about-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

/* Hero Section */
.about-hero {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 3rem;
  align-items: center;
  margin-bottom: 4rem;
  padding: 3rem;
  border-radius: 16px;
}

.about-photo {
  position: relative;
  width: 200px;
  height: 200px;
}

.profile-photo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  border: 3px dashed currentColor;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.placeholder-icon {
  margin-bottom: 0.5rem;
}

.about-intro {
  min-width: 0;
}

.about-name {
  margin: 0 0 0.5rem 0;
}

.about-title {
  margin: 0 0 1.5rem 0;
}

.about-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.meta-icon {
}

/* Bio Section */
.about-bio {
  margin-bottom: 4rem;
}

.bio-intro {
  margin-bottom: 3rem;
}

.intro-text {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  border-radius: 12px;
  border-left: 4px solid currentColor;
  opacity: 0.9;
}

.bio-sections {
  display: grid;
  gap: 2rem;
}

.bio-section {
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid currentColor;
  border-opacity: 0.1;
}

.bio-section h3 {
  margin: 0 0 1rem 0;
  border-bottom: 2px solid currentColor;
  padding-bottom: 0.5rem;
}

.expertise-list {
  list-style: disc;
  padding-left: 1.5rem;
  margin: 0;
}

.expertise-list li {
  margin-bottom: 0.5rem;
}

/* Achievements Section */
.about-achievements {
  margin-bottom: 4rem;
}

.section-title {
  text-align: center;
  margin: 0 0 1rem 0;
}

.section-description {
  text-align: center;
  margin: 0 0 3rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.achievement-card {
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid currentColor;
  border-opacity: 0.1;
  text-align: center;
  transition: transform 0.2s ease;
}

.achievement-card:hover {
  transform: translateY(-4px);
}

.achievement-icon {
  margin-bottom: 1rem;
}

.achievement-title {
  margin: 0 0 1rem 0;
}

.achievement-description {
  margin: 0 0 1rem 0;
}

.achievement-year {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  border: 1px solid currentColor;
  opacity: 0.8;
}

/* Social Media Section */
.about-social {
  margin-bottom: 4rem;
}

.social-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Social Card Styles */
.social-card {
  border-radius: 12px;
  border: 1px solid currentColor;
  border-opacity: 0.1;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.social-card:hover {
  transform: translateY(-4px);
}

.social-card-link {
  display: block;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
}

.social-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.social-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.social-card-icon i {
  font-size: 24px;
}

.social-card-title h3 {
  margin: 0;
}

.social-card-handle {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  opacity: 0.7;
}

.social-card-description {
  margin: 0 0 1rem 0;
  opacity: 0.8;
}

.social-card-footer {
  display: flex;
  justify-content: flex-end;
}

.social-card-cta {
  opacity: 0.8;
}

/* Call to Action Section */
.about-cta {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: #fff;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.3);
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-icon {
  margin-bottom: 1rem;
}

.cta-title {
  margin: 0 0 1rem 0;
}

.cta-description {
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
}

.cta-button.primary {
  background: #fff;
  color: #007bff;
}

.cta-button.primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-page {
    padding: 1rem;
  }

  .about-hero {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
    padding: 2rem;
  }

  .about-photo {
    justify-self: center;
  }



  .about-meta {
    justify-content: center;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .social-cards-grid {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
