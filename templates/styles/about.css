/* About Page Styles */
.about-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: #333;
}

/* Hero Section */
.about-hero {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 3rem;
  align-items: center;
  margin-bottom: 4rem;
  padding: 3rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.about-photo {
  position: relative;
  width: 200px;
  height: 200px;
}

.profile-photo {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 4px solid #fff;
}

.photo-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border: 3px dashed #dee2e6;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.about-intro {
  min-width: 0;
}

.about-name {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #212529;
}

.about-title {
  font-size: 1.5rem;
  font-weight: 400;
  margin: 0 0 1.5rem 0;
  color: #6c757d;
}

.about-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: #495057;
}

.meta-icon {
  font-size: 1.2rem;
}

/* Bio Section */
.about-bio {
  margin-bottom: 4rem;
}

.bio-intro {
  margin-bottom: 3rem;
}

.intro-text {
  font-size: 1.25rem;
  color: #495057;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #007bff;
}

.bio-sections {
  display: grid;
  gap: 2rem;
}

.bio-section {
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
}

.bio-section h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #212529;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

.expertise-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 0.75rem;
}

.expertise-list li {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #007bff;
  font-weight: 500;
}

/* Achievements Section */
.about-achievements {
  margin-bottom: 4rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 1rem 0;
  color: #212529;
}

.section-description {
  text-align: center;
  font-size: 1.1rem;
  color: #6c757d;
  margin: 0 0 3rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.achievement-card {
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.achievement-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.achievement-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.achievement-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #212529;
}

.achievement-description {
  color: #6c757d;
  margin: 0 0 1rem 0;
}

.achievement-year {
  font-size: 0.9rem;
  font-weight: 600;
  color: #007bff;
  background: #e7f3ff;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

/* Social Media Section */
.about-social {
  margin-bottom: 4rem;
}

.social-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

/* Social Card Styles */
.social-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.social-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.social-card-link {
  display: block;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
}

.social-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.social-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  flex-shrink: 0;
}

.social-card-icon svg {
  width: 24px;
  height: 24px;
}

.social-card-title h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #212529;
}

.social-card-handle {
  font-size: 0.9rem;
  color: #6c757d;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.social-card-description {
  color: #6c757d;
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
}

.social-card-footer {
  display: flex;
  justify-content: flex-end;
}

.social-card-cta {
  font-size: 0.9rem;
  font-weight: 600;
  color: #007bff;
}

/* Call to Action Section */
.about-cta {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: #fff;
  border-radius: 16px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.3);
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.cta-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.cta-description {
  font-size: 1.1rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.cta-button.primary {
  background: #fff;
  color: #007bff;
}

.cta-button.primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-page {
    padding: 1rem;
  }

  .about-hero {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
    padding: 2rem;
  }

  .about-photo {
    justify-self: center;
  }

  .about-name {
    font-size: 2.5rem;
  }

  .about-meta {
    justify-content: center;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .social-cards-grid {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
