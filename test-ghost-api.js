#!/usr/bin/env node

const fs = require('fs');
const yaml = require('js-yaml');
const GhostAdminAPI = require('@tryghost/admin-api');
const GhostContentAPI = require('@tryghost/content-api');

// Load config
const configContent = fs.readFileSync('ghost.yml', 'utf8');
const config = yaml.load(configContent);
const integration = config.integrations.page_sync;

console.log('🔧 Testing Ghost API connection...');
console.log(`Admin API Key: ${integration.admin_api_key.substring(0, 20)}...`);
console.log(`Content API Key: ${integration.content_api_key}`);

// Test Content API first (read-only, should work)
const contentAPI = new GhostContentAPI({
  url: 'https://solnic.dev',
  key: integration.content_api_key,
  version: 'v5.0'
});

console.log('\n📖 Testing Content API...');
contentAPI.pages.browse({ limit: 1 })
  .then(pages => {
    console.log('✅ Content API works!');
    console.log(`Found ${pages.length} page(s)`);
    if (pages.length > 0) {
      console.log(`First page: "${pages[0].title}" (slug: ${pages[0].slug})`);
    }
    
    // Now test Admin API
    console.log('\n🔐 Testing Admin API...');
    const adminAPI = new GhostAdminAPI({
      url: 'https://solnic.dev',
      key: integration.admin_api_key,
      version: 'v5.0'
    });
    
    return adminAPI.pages.browse({ limit: 1 });
  })
  .then(pages => {
    console.log('✅ Admin API works!');
    console.log(`Found ${pages.length} page(s) via Admin API`);
    
    // Look for github-sponsors page specifically
    console.log('\n🔍 Looking for github-sponsors page...');
    const adminAPI = new GhostAdminAPI({
      url: 'https://solnic.dev',
      key: integration.admin_api_key,
      version: 'v5.0'
    });
    
    return adminAPI.pages.browse({
      filter: 'slug:github-sponsors',
      limit: 1
    });
  })
  .then(pages => {
    if (pages.length > 0) {
      console.log('✅ Found github-sponsors page!');
      console.log(`Title: "${pages[0].title}"`);
      console.log(`ID: ${pages[0].id}`);
      console.log(`Slug: ${pages[0].slug}`);
      console.log(`Status: ${pages[0].status}`);
    } else {
      console.log('❌ github-sponsors page not found');
      console.log('💡 Please create the page in Ghost admin first');
    }
  })
  .catch(error => {
    console.error('❌ Error:', error.message);
    if (error.message.includes('Authorization')) {
      console.error('💡 Check that your Admin API key has the correct permissions');
      console.error('💡 Make sure the integration is created in Ghost Admin → Settings → Integrations');
    }
  });
