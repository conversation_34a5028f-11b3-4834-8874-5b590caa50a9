🚀 Starting Ghost.io sync...
✅ Ghost configuration loaded
✅ Ghost Admin API initialized
🔍 Looking for open source page...
✅ Found page: "Open Source" (ID: 6899d385064a4f000166c4b6)
📊 Reading open source projects data...
📈 Reading GitHub statistics...
✅ Uploaded elixir-drops.png to Ghost CMS
✅ Uploaded hanami.png to Ghost CMS
✅ Uploaded rom-rb.png to Ghost CMS
✅ Uploaded dry-rb.png to Ghost CMS

🔍 Dry run - would update page with:
📊 Total projects: 8
📊 Current projects: 1
📊 Past projects: 7
📄 HTML length: 28343 characters

🔍 HTML preview (first 300 characters):
<style>
.open-source-portfolio {
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.portfolio-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 1rem...

🔍 Dry run completed - no changes were made
